#ifndef __DEVICE_CONTROL_H__
#define __DEVICE_CONTROL_H__

#include <Arduino.h>
#define LED_GREEN_BRIGHTNESS 48
#define LED_RED_BRIGHTNESS 48
#define LED_BLUE_BRIGHTNESS 100
#define LED_YELLOW_BRIGHTNESS 48
#define LED_SPRING_GREEN_BRIGHTNESS 56

// 状态变量
enum LedMode {
    MODE_WIFI_CONNECT_STATUS,
    MODE_TUYAIOT_STATUS,
    MODE_PUMP_FLASH,
    MODE_OZONE_FLASH,
    MODE_FACTORY_RESET_BLINK,
    MODE_BLACK_FLASH
};

#ifdef __cplusplus
extern "C" {
#endif
void device_control_init();
void device_control_loop();
void device_control_tuya_iot_status(bool connected);
void device_control_wifi_connected_flash(bool connected);
void handle_button6_click();
void handle_button7_click();
bool is_pump_ozone_running();
#ifdef __cplusplus
}
#endif
#endif // __DEVICE_CONTROL_H__
