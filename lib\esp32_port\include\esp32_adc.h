#pragma once
#include <Arduino.h>

// ESP-IDF 5.0+ 新版ADC API
#if ESP_IDF_VERSION >= ESP_IDF_VERSION_VAL(5, 0, 0)
#include "esp_adc/adc_oneshot.h"
#include "esp_adc/adc_continuous.h"
#include "esp_adc/adc_cali.h"
#include "esp_adc/adc_cali_scheme.h"
#else
// ESP-IDF 4.x 旧版ADC API
#include "esp_adc_cal.h"
#include "driver/adc.h"
#endif

typedef struct
{
    float voltage_divider_factor; // 电压分压系数
    // 单次采样
    uint8_t adc_pin;
    adc_unit_t adc_unit;
    adc_channel_t adc_channel;   // 使用通用的adc_channel_t类型
    adc_atten_t adc_atten;       // 0mV ~ 2500mV/0 mV ~ 3100 mV 衰减范围
#if ESP_IDF_VERSION >= ESP_IDF_VERSION_VAL(5, 0, 0)
    adc_bitwidth_t adc_width;    // 12位分辨率 (新版API)
#else
    adc_bits_width_t adc_width;  // 12位分辨率 (旧版API)
#endif
    const uint16_t default_vref; // 默认参考电压，单位mV
    // 连续采样
    uint16_t adc1_chan_mask;
    uint16_t adc2_chan_mask; // example for ADC2, BIT(0)
    adc_channel_t channel[1];

#if ESP_IDF_VERSION >= ESP_IDF_VERSION_VAL(5, 0, 0)
    // 新版API结构体
    adc_oneshot_unit_handle_t adc_handle;        // 单次采样句柄（共享）
    adc_continuous_handle_t adc_continuous_handle; // 连续采样句柄
    adc_cali_handle_t cali_handle;
    bool cali_enabled;
    bool handle_shared;                          // 标记句柄是否为共享
#else
    // 旧版API结构体
    esp_adc_cal_characteristics_t adc_chars;
    esp_adc_cal_value_t cal_type;
#endif
    uint8_t adc_single_sample_num; // 采样次数 多次采样求平均，采样次数，用于平均，降低噪声
    uint8_t adc_single_sample_delay;   // 采样间隔，单位ms
} adc_config_t;

#ifdef __cplusplus
extern "C" {
#endif
void adc_calibration(adc_config_t *adc_config);
void adc_single_init(adc_config_t *adc_config);
void adc_continuous_init(adc_config_t *adc_config);
void adc_deinit(adc_config_t *adc_config);
float ReadVoltageContinuous(adc_config_t *adc_config);
float ReadVoltageSingle(adc_config_t *adc_config);
#ifdef __cplusplus
}
#endif