#ifndef RMT_WS2812_H
#define RMT_WS2812_H

#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief Initialize the RMT driver for WS2812B.
 *
 * @param gpio_num GPIO number for the WS2812B data line.
 */
void rmt_ws2812_init(int gpio_num);

/**
 * @brief Deinitialize the RMT driver for WS2812B.
 *
 * This function uninstalls the RMT driver and releases its resources.
 */
void rmt_ws2812_deinit(void);

/**
 * @brief Set the color of a single WS2812B LED.
 *
 * @param r Red component (0-255).
 * @param g Green component (0-255).
 * @param b Blue component (0-255).
 */
void rmt_ws2812_set_pixel(uint8_t r, uint8_t g, uint8_t b);
void rmt_ws2812_color_with_hold(uint8_t r, uint8_t g, uint8_t b);
uint8_t rmt_ws2812_scale8(uint8_t val, uint8_t scale);

#ifdef __cplusplus
}
#endif

#endif // RMT_WS2812_H
