#include "device_control.h"
#include <OneButton.h>
#include "rmt_ws2812.h"
#include <math.h>
#include "driver/gpio.h"
#include "tuya_log.h"
#include "shipping.h"
#include "esp32_adc.h"
#include "tuya_config.h"
#include "fault.h"

int watering_use_time = 60;
unsigned long pump_ozone_start_time_ms = 0;
int down_time_last_report_time = 0;
int down_time_report_interval = 20; // 秒
int factory_blink_delay = 500;
bool reset_triggered = false;
extern unsigned char current_fault_code;

adc_config_t adc_config_pump = {
    .voltage_divider_factor = 1.00,
    .adc_pin = PIN_ADC_PUMP,
    .adc_unit = ADC_UNIT_1,
    .adc_channel = ADC_CHANNEL_3,
    .adc_atten = ADC_ATTEN_DB_12,
#if ESP_IDF_VERSION >= ESP_IDF_VERSION_VAL(5, 0, 0)
    .adc_width = ADC_BITWIDTH_12,
#else
    .adc_width = ADC_WIDTH_BIT_12,
#endif
    .default_vref = 1100,
    .adc1_chan_mask = BIT(3),
    .adc2_chan_mask = 0x0,
    .channel = {ADC_CHANNEL_3},
    .adc_single_sample_num = 16,
    .adc_single_sample_delay = 2,
};

adc_config_t adc_config_vbat = {
    .voltage_divider_factor = 2.00,
    .adc_pin = PIN_ADC_VBAT,
    .adc_unit = ADC_UNIT_1,
    .adc_channel = ADC_CHANNEL_4,
    .adc_atten = ADC_ATTEN_DB_12,
#if ESP_IDF_VERSION >= ESP_IDF_VERSION_VAL(5, 0, 0)
    .adc_width = ADC_BITWIDTH_12,
#else
    .adc_width = ADC_WIDTH_BIT_12,
#endif
    .default_vref = 1100,
    .adc1_chan_mask = BIT(4),
    .adc2_chan_mask = 0x0,
    .channel = {ADC_CHANNEL_4},
    .adc_single_sample_num = 1,
    .adc_single_sample_delay = 0,
};

// OneButton 实例 (低电平有效, 启用内部上拉)
// Declared static to avoid conflicts with other OneButton instances in the project (e.g., in shipping.cpp)
static OneButton button6(PIN_PUMP_BUTTON, true, true);
static OneButton button7(PIN_OZONE_BUTTON, true, true);

volatile LedMode current_led_mode = MODE_TUYAIOT_STATUS;
volatile bool tuyaiot_connected = false;
volatile bool wifi_connected = false;
bool gpios_on = false; // 新增一个独立的GPIO状态变量
int pump_dry_count = 0; // 电机空转运行计数
int pump_pipeBlock_count = 0; // 管道堵塞运行计数
bool pump_dry_run_flag = false; // 电机空转运行标志
bool pump_pipeBlock_run_flag = false; // 管道堵塞标志

// 函数声明
void handle_button6_click();
void handle_button7_click();
void handle_long_press_start();
void handle_during_long_press(void *oneButton_param);
void handle_long_press_stop();
void led_task(void *pvParameters);
void bind_button_events(); // 新增：绑定按键事件的统一函数
void pump_ozone_stop();
extern "C" void report_tuya(int dpid, int value, bool on);

extern "C" bool is_pump_zone_run() {
    return (current_led_mode == MODE_PUMP_FLASH || current_led_mode == MODE_OZONE_FLASH);
}

void device_control_init()
{
    // 初始化 ADC采样电机和电池
    adc_single_init(&adc_config_pump);
    adc_calibration(&adc_config_pump);
    adc_single_init(&adc_config_vbat);
    adc_calibration(&adc_config_vbat);
    // 初始化 GPIO
    pinMode(PIN_PUMP, OUTPUT);
    pinMode(PIN_OZONE, OUTPUT);
    pinMode(PIN_PUMP_OZONE_POWER, OUTPUT);
    digitalWrite(PIN_PUMP, LOW);
    digitalWrite(PIN_OZONE, LOW);
    digitalWrite(PIN_PUMP_OZONE_POWER, LOW);  //关断水泵和臭氧总电源

    // 检查按钮是否仍然被按下（可能是从shipping模式唤醒）
    bool button6_pressed = (digitalRead(PIN_PUMP_BUTTON) == LOW);
    bool button7_pressed = (digitalRead(PIN_OZONE_BUTTON) == LOW);

    if (button6_pressed || button7_pressed) {
        TY_LOGI("Button still pressed during init, waiting for release...");
        // 等待按钮释放，最多等待5秒
        unsigned long wait_start = millis();
        while ((digitalRead(PIN_PUMP_BUTTON) == LOW || digitalRead(PIN_OZONE_BUTTON) == LOW) &&
               (millis() - wait_start < 5000)) {
            vTaskDelay(pdMS_TO_TICKS(50));
        }

        // 检查是否因为超时退出循环
        if (digitalRead(PIN_PUMP_BUTTON) == LOW || digitalRead(PIN_OZONE_BUTTON) == LOW) {
            TY_LOGW("Button still pressed after 5s timeout! Delaying button initialization...");
            // 如果按钮仍然被按下，延迟初始化按钮事件
            // 先初始化按钮对象但不绑定事件处理器
            button6.setDebounceMs(50);
            button7.setDebounceMs(50);

            // 创建一个延迟任务来等待按钮释放后再绑定事件
            xTaskCreate([](void* param) {
                TY_LOGI("Delayed button initialization task started");
                // 继续等待按钮释放
                while (digitalRead(PIN_PUMP_BUTTON) == LOW || digitalRead(PIN_OZONE_BUTTON) == LOW) {
                    vTaskDelay(pdMS_TO_TICKS(100));
                }
                TY_LOGI("Button finally released, binding events now");

                // 现在安全地绑定按键事件
                bind_button_events();

                TY_LOGI("Button events bound successfully");
                vTaskDelete(NULL); // 删除自己
            }, "delayed_button_init", 2048, NULL, 1, NULL);

            TY_LOGI("Button initialization delayed due to pressed state");
            // 不要提前返回，继续执行后续的任务创建
        } else {
            TY_LOGI("Button released, continuing normal initialization");
            // 正常情况下立即绑定按键事件
            bind_button_events();
        }
    } else {
        // 按钮未被按下，正常绑定事件
        bind_button_events();
    }
}

void device_control_loop()
{
    button6.tick();
    button7.tick();
}

void device_control_tuya_iot_status(bool connected)
{
    tuyaiot_connected = connected;
    current_led_mode = MODE_TUYAIOT_STATUS;
}

void device_control_wifi_connected_flash(bool connected)
{
    current_led_mode = MODE_WIFI_CONNECT_STATUS;
    wifi_connected = connected;
}

void handle_button6_click() // PUMP
{
    TY_LOGI("Button 6 clicked");
    pump_dry_count = 0;
    pump_pipeBlock_count = 0;
    // Case 1: GPIOs are on and we are showing BLUE. Turn everything off.
    if (gpios_on && current_led_mode == MODE_PUMP_FLASH ) {
        gpios_on = false;
        pump_ozone_stop();    // 停止水泵和臭氧
    }
    // Case 2: GPIOs are off, OR we are showing GREEN. Turn on GPIOs and switch to RED.
    else {
        if (is_fault_active(FAULT_LOW_BATTERY_BIT)) {   // 低电量关闭水泵
            gpios_on = false;
            pump_ozone_stop();
        }
        else {
            gpios_on = true;
            current_led_mode = MODE_PUMP_FLASH;
            down_time_last_report_time = 0;
            digitalWrite(PIN_PUMP_OZONE_POWER, HIGH);  //水泵和臭氧总电源
            vTaskDelay(pdMS_TO_TICKS(50));
            digitalWrite(PIN_PUMP, HIGH);
            digitalWrite(PIN_OZONE, LOW);
            pump_ozone_start_time_ms = millis();
            report_tuya(SWITCH_PUMP_ID, 0, true);
            report_tuya(SWITCH_OZONE_ID, 0, false);
            report_tuya(WATERING_COUNT_DOWN_ID, watering_use_time, true); //开始倒计时显示
        }
    }
}

void handle_button7_click() // OZONE
{
    TY_LOGI("Button 7 clicked");
    pump_dry_count = 0;
    pump_pipeBlock_count = 0;
    // Case 1: GPIOs are on and we are showing GREEN. Turn everything off.
    if (gpios_on && current_led_mode == MODE_OZONE_FLASH) {
        gpios_on = false;
        pump_ozone_stop();    // 停止水泵和臭氧
    }
    // Case 2: GPIOs are off, OR we are showing BLUE. Turn on GPIOs and switch to GREEN.
    else {
        if (is_fault_active(FAULT_LOW_BATTERY_BIT)) {   // 低电量关闭水泵
            gpios_on = false;
            pump_ozone_stop(); 
        }
        else {
            gpios_on = true;
            current_led_mode = MODE_OZONE_FLASH;
            down_time_last_report_time = 0;
            digitalWrite(PIN_PUMP_OZONE_POWER, HIGH);  //水泵和臭氧总电源
            vTaskDelay(pdMS_TO_TICKS(50));
            digitalWrite(PIN_PUMP, HIGH);
            digitalWrite(PIN_OZONE, HIGH);
            pump_ozone_start_time_ms = millis();
            report_tuya(SWITCH_OZONE_ID, 0, true);
            report_tuya(SWITCH_PUMP_ID, 0, false);
            report_tuya(WATERING_COUNT_DOWN_ID, watering_use_time, true); //开始倒计时显示
        }
    }
}

void handle_long_press_start()
{
    TY_LOGI("Long press start detected. Starting factory reset blink.");
    factory_blink_delay = 500;
    current_led_mode = MODE_FACTORY_RESET_BLINK;
    reset_triggered = false;
}

void handle_during_long_press(void *oneButton_param)
{
    unsigned long button_press_time = ((OneButton *)oneButton_param)->getPressedMs();

    // 检查任一按钮的长按时间是否超过10秒
    if (button_press_time >= 10000) {
        //TY_LOGI("Factory reset triggered by holding for 10s!");
        reset_triggered = true;
        current_led_mode = MODE_BLACK_FLASH; // 改为黑色闪烁，表示重置中
    }
}

void handle_long_press_stop()
{
    TY_LOGI("Long press stopped.");
    // 如果长按被释放（无论是否触发了重置），都恢复正常状态
    // 如果已经触发重置，设备会重启，这里只是一个安全回退
    if (reset_triggered) {
        pump_ozone_stop();    // 停止水泵和臭氧
        factory_reset();
        ESP.restart();
    }
    else {
        current_led_mode = MODE_TUYAIOT_STATUS;
    }
}

bool is_pump_ozone_running()
{
    return (MODE_PUMP_FLASH == current_led_mode || MODE_OZONE_FLASH == current_led_mode);
}

void pump_ozone_stop()
{
    current_led_mode = MODE_TUYAIOT_STATUS;
    digitalWrite(PIN_PUMP, LOW);
    digitalWrite(PIN_OZONE, LOW);
    digitalWrite(PIN_PUMP_OZONE_POWER, LOW); // 水泵和臭氧总电源
    report_tuya(SWITCH_OZONE_ID, 0, false);
    report_tuya(SWITCH_PUMP_ID, 0, false);
    report_tuya(WATERING_COUNT_DOWN_ID, 0, true); // 停止水泵和臭氧后，重置倒计时
}

void pump_ozone_check(bool *is_pump_dry_run, bool *is_pump_pipeBlock)
{
    int elapsed = (millis() - pump_ozone_start_time_ms) / 1000;
    // --- 功耗优化：仅在需要时获取ADC电源 ---
    adc_power_acquire();
    float voltage = ReadVoltageSingle(&adc_config_pump);
    adc_power_release();

    TY_LOGD("Pump Voltage: %.2fV", voltage);
    if (voltage < 0.70f) {
        *is_pump_dry_run = true;
        if (*is_pump_dry_run) {
            pump_dry_count++;
        } else if (pump_dry_count > 0) {
            pump_dry_count--;
        }
    }
    else if (voltage > 1.70f) {
        *is_pump_pipeBlock = true;
        if (*is_pump_pipeBlock) {
            pump_pipeBlock_count++;
        } else if (pump_pipeBlock_count > 0) {
            pump_pipeBlock_count--;
        }
    }
    else {
        *is_pump_dry_run = false;
        *is_pump_pipeBlock = false;
    }

    // 连续20次检测到空转或灌溉时长到达，则退出水泵模式
    if (pump_dry_count >= 20)
    {
        pump_dry_run_flag = true;
        pump_ozone_stop();
    }
    else if (pump_pipeBlock_count >= 20)
    {
        pump_pipeBlock_run_flag = true;
        pump_ozone_stop();
    }
    else {
        pump_dry_run_flag = false;
        pump_pipeBlock_run_flag = false;
        if (elapsed >= watering_use_time)
            pump_ozone_stop();
        else {
            // 周期性上报
            int time_since_last_report = elapsed - down_time_last_report_time;
            if (time_since_last_report >= down_time_report_interval)
            {
                int remaining = (elapsed >= watering_use_time) ? 0 : (watering_use_time - elapsed);
                down_time_last_report_time = elapsed;
                report_tuya(WATERING_COUNT_DOWN_ID, remaining, true);
            }
        }
    }
}

void led_task(void *pvParameters)
{
    bool led_on = false;
    int count = 0;
    UBaseType_t stackLeft;
    uint8_t breathing_phase = 0; // 恢复使用 uint8_t 作为相位索引
    uint8_t last_brightness = 0;
    bool is_pump_dry_run = false, is_pump_pipeBlock = false;
    uint16_t delay_ms = 0;
    uint8_t brightness_table[256]; // 恢复亮度表

    // 初始化 RMT
    rmt_ws2812_init(PIN_LED);

    // 预计算亮度表
    for (int i = 0; i < 256; i++) {
        // 使用 sinf 生成 0-1 的浮点数，然后映射到 0-LED_GREEN_BRIGHTNESS 的亮度 (降低最大亮度以节省功耗)
        float sin_value = (sinf(i * (2 * M_PI) / 256.0f) + 1.0f) / 2.0f;
        brightness_table[i] = (uint8_t)(sin_value * LED_GREEN_BRIGHTNESS);
    }

    while (true) {
        switch (current_led_mode) {
            case MODE_TUYAIOT_STATUS:
                if (tuyaiot_connected) {
                    // TuyaIoT 已连接：呼吸灯模式 (Green)
                    breathing_phase += 3; // 调整呼吸速度

                    uint8_t brightness = brightness_table[breathing_phase];

                    if (brightness == 0) {
                        if (last_brightness != 0) {
                            // Light is transitioning from ON to OFF, turn it off and start the long delay.
                            rmt_ws2812_color_with_hold(0, 0, 0);
                            last_brightness = 0;
                            vTaskDelay(pdMS_TO_TICKS(500));
                        } else {
                            // Light is already off, just do a short delay to prevent busy-looping.
                            vTaskDelay(pdMS_TO_TICKS(10));
                        }
                    } else {
                        uint8_t delta = abs(brightness - last_brightness);
                        uint8_t threshold = map(brightness, 0, LED_GREEN_BRIGHTNESS, 1, 4);
                        if (delta >= threshold) {
                            rmt_ws2812_color_with_hold(0, brightness, 0); // Green
                            last_brightness = brightness;
                        }
                        delay_ms = map(brightness, 0, LED_GREEN_BRIGHTNESS, 75, 25);
                        vTaskDelay(pdMS_TO_TICKS(delay_ms));
                    }
                } else {
                    // TuyaIoT 未连接：闪烁模式 (Red)
                    led_on = !led_on;
                    if (led_on) {
                        rmt_ws2812_color_with_hold(rmt_ws2812_scale8(255, LED_RED_BRIGHTNESS), 0, 0); // Red at 25%
                    } else {
                        rmt_ws2812_color_with_hold(0, 0, 0); // Off
                    }
                    vTaskDelay(pdMS_TO_TICKS(500)); // 闪烁间隔
                }
                break;

            case MODE_PUMP_FLASH:
                if (!is_pump_dry_run && !is_pump_pipeBlock) {
                    // Match FastLED's SpringGreen (R=0, G=255, B=127) at a scaled brightness.
                    rmt_ws2812_color_with_hold(0, rmt_ws2812_scale8(255, LED_SPRING_GREEN_BRIGHTNESS), rmt_ws2812_scale8(127, LED_SPRING_GREEN_BRIGHTNESS));
                } else {
                    led_on = !led_on;
                    if (led_on) {
                        rmt_ws2812_color_with_hold(rmt_ws2812_scale8(255, LED_RED_BRIGHTNESS), 0, 0); // Red
                    } else {
                        rmt_ws2812_color_with_hold(0, 0, 0); // Off
                    }
                }
                vTaskDelay(pdMS_TO_TICKS(250));
                pump_ozone_check(&is_pump_dry_run, &is_pump_pipeBlock);
                break;

            case MODE_OZONE_FLASH:
                if (!is_pump_dry_run && !is_pump_pipeBlock) {
                    rmt_ws2812_color_with_hold(0, 0, rmt_ws2812_scale8(255, LED_BLUE_BRIGHTNESS)); // Blue
                } else {
                    led_on = !led_on;
                    if (led_on) {
                        rmt_ws2812_color_with_hold(rmt_ws2812_scale8(255, LED_RED_BRIGHTNESS), 0, 0); // Red
                    } else {
                        rmt_ws2812_color_with_hold(0, 0, 0); // Off
                    }
                }
                vTaskDelay(pdMS_TO_TICKS(250));
                pump_ozone_check(&is_pump_dry_run, &is_pump_pipeBlock);
                break;
            
            case MODE_FACTORY_RESET_BLINK:
                rmt_ws2812_color_with_hold(rmt_ws2812_scale8(255, LED_RED_BRIGHTNESS), 0, 0); // Red
                vTaskDelay(pdMS_TO_TICKS(factory_blink_delay));
                rmt_ws2812_color_with_hold(0, 0, 0); // Off
                vTaskDelay(pdMS_TO_TICKS(factory_blink_delay));

                if (factory_blink_delay > 50) {
                    factory_blink_delay -= 45; // 加速闪烁
                }
                break;

            case MODE_WIFI_CONNECT_STATUS:
                led_on = !led_on;
                if (led_on) {
                    if (wifi_connected) {
                        rmt_ws2812_color_with_hold(rmt_ws2812_scale8(255, LED_YELLOW_BRIGHTNESS), rmt_ws2812_scale8(255, LED_YELLOW_BRIGHTNESS), 0); // Yellow
                    } else {
                        rmt_ws2812_color_with_hold(rmt_ws2812_scale8(255, LED_RED_BRIGHTNESS), 0, 0); // Red
                    }
                } else {
                    rmt_ws2812_color_with_hold(0, 0, 0); // Off
                }
                vTaskDelay(pdMS_TO_TICKS(250));
                break;
            case MODE_BLACK_FLASH:
                rmt_ws2812_color_with_hold(0, 0, 0); // Off
                vTaskDelay(pdMS_TO_TICKS(250));
                break;
        }

        if (pump_dry_run_flag) {
            if (!is_fault_active(FAULT_LACK_WATER_BIT)) {
                set_fault(FAULT_LACK_WATER_BIT);
                report_tuya(FAULT_ID, current_fault_code, false);
            }
        } else {
            if (is_fault_active(FAULT_LACK_WATER_BIT)) {
                clear_fault(FAULT_LACK_WATER_BIT);
                report_tuya(FAULT_ID, current_fault_code, false);
            }
        }

        if (pump_pipeBlock_run_flag) {
            if (!is_fault_active(FAULT_PIPE_BLOCKAGE_BIT)) {
                set_fault(FAULT_PIPE_BLOCKAGE_BIT);
                report_tuya(FAULT_ID, current_fault_code, false);
            }
        } else {
            if (is_fault_active(FAULT_PIPE_BLOCKAGE_BIT)) {
                clear_fault(FAULT_PIPE_BLOCKAGE_BIT);
                report_tuya(FAULT_ID, current_fault_code, false);
            }
        }
#if 0
        // 每约1-2秒打印一次采样结果
        if (++count >= 4) {
            float vref = ReadVoltageSingle(&adc_config_pump);
            TY_LOGI("[led_task] Pump Vref: %f", vref);
            count = 0;
        }
#endif
#if 0
        // 每约5秒打印一次堆栈剩余空间
        if (++count >= debug_stack_count) {
            stackLeft = uxTaskGetStackHighWaterMark(NULL);
            TY_LOGD("[led_task] Stack high water mark: %d", stackLeft);
            count = 0;
            debug_stack_count = 40;
        }
#endif
    }
}

/**
 * @brief 绑定按键事件的统一函数
 */
void bind_button_events() {
    TY_LOGI("Binding button events");

    // 绑定按钮6事件
    button6.attachClick(handle_button6_click);
    button6.attachLongPressStart(handle_long_press_start);
    button6.attachDuringLongPress(handle_during_long_press, &button6);
    button6.attachLongPressStop(handle_long_press_stop);
    button6.setPressMs(3000); // 长按3000ms后开始检测
    button6.setDebounceMs(50); // 增加去抖动以防止物理按键抖动导致误触发

    // 绑定按钮7事件
    button7.attachClick(handle_button7_click);
    button7.attachLongPressStart(handle_long_press_start);
    button7.attachDuringLongPress(handle_during_long_press, &button7);
    button7.attachLongPressStop(handle_long_press_stop);
    button7.setPressMs(3000); // 长按3000ms后开始检测
    button7.setDebounceMs(50); // 增加去抖动以防止物理按键抖动导致误触发
}

