/*
 * 适配esp32c3 S3 H2芯片ADC，其它系列可能需要微调
 * 参考文档：
 * https://docs.espressif.com/projects/esp-idf/en/latest/esp32c3/api-reference/peripherals/adc.html
 * https://espressif-docs.readthedocs-hosted.com/projects/arduino-esp32/en/latest/api/adc.html
 * https://github.com/espressif/esp-idf/blob/v4.4/examples/peripherals/adc/dma_read/main/adc_dma_example_main.c
 */
#include "esp32_adc.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

// ESP-IDF 5.0+ 新版ADC API
#if ESP_IDF_VERSION >= ESP_IDF_VERSION_VAL(5, 0, 0)
#include "esp_adc/adc_oneshot.h"
#include "esp_adc/adc_continuous.h"
#include "esp_adc/adc_cali.h"
#include "esp_adc/adc_cali_scheme.h"

// 全局ADC句柄管理
static adc_oneshot_unit_handle_t g_adc1_handle = NULL;
static adc_oneshot_unit_handle_t g_adc2_handle = NULL;
static int g_adc1_ref_count = 0;
static int g_adc2_ref_count = 0;
#endif

// 连续采样
#define TIMES 256
#define GET_UNIT(x) ((x >> 3) & 0x1)
#if CONFIG_IDF_TARGET_ESP32C3 || CONFIG_IDF_TARGET_ESP32H2
#define ADC_RESULT_BYTE 4
#define ADC_CONV_LIMIT_EN 0
#define ADC_CONV_MODE ADC_CONV_ALTER_UNIT // ESP32C3 only supports alter mode
#define ADC_OUTPUT_TYPE ADC_DIGI_OUTPUT_FORMAT_TYPE2
#elif CONFIG_IDF_TARGET_ESP32S3
#define ADC_RESULT_BYTE 4
#define ADC_CONV_LIMIT_EN 0
#define ADC_CONV_MODE ADC_CONV_BOTH_UNIT
#define ADC_OUTPUT_TYPE ADC_DIGI_OUTPUT_FORMAT_TYPE2
#endif

void adc_calibration(adc_config_t *adc_config)
{
#if ESP_IDF_VERSION >= ESP_IDF_VERSION_VAL(5, 0, 0)
    // ESP-IDF 5.0+ 新版校准API
    adc_config->cali_enabled = false;

    // 检查支持的校准方案
    adc_cali_scheme_ver_t scheme_mask;
    esp_err_t ret = adc_cali_check_scheme(&scheme_mask);
    if (ret == ESP_OK) {
        if (scheme_mask & ADC_CALI_SCHEME_VER_CURVE_FITTING) {
            // 尝试创建曲线拟合校准句柄
            adc_cali_curve_fitting_config_t cali_config = {
                .unit_id = adc_config->adc_unit,
                .atten = adc_config->adc_atten,
                .bitwidth = adc_config->adc_width,
            };

            ret = adc_cali_create_scheme_curve_fitting(&cali_config, &adc_config->cali_handle);
            if (ret == ESP_OK) {
                adc_config->cali_enabled = true;
                printf("ADC: Curve fitting calibration scheme created\n");
            } else {
                printf("ADC: Curve fitting calibration creation failed (ret=%d)\n", ret);
                adc_config->cali_enabled = false;
            }
        } else {
            printf("ADC: Curve fitting calibration not supported on this chip\n");
            adc_config->cali_enabled = false;
        }
    } else {
        printf("ADC: Failed to check calibration scheme support (ret=%d)\n", ret);
        adc_config->cali_enabled = false;
    }
#else
    // ESP-IDF 4.x 旧版校准API
    adc_config->cal_type = esp_adc_cal_characterize(adc_config->adc_unit, adc_config->adc_atten, adc_config->adc_width, adc_config->default_vref, &adc_config->adc_chars);
    if (adc_config->cal_type == ESP_ADC_CAL_VAL_EFUSE_TP)
    {
        printf("ADC: Two Point values stored in eFuse\n");
    }
    else if (adc_config->cal_type == ESP_ADC_CAL_VAL_EFUSE_TP_FIT)
    {
        printf("ADC: Two Point values and fitting curve coefficients stored in eFuse\n");
    }
    else if (adc_config->cal_type == ESP_ADC_CAL_VAL_EFUSE_VREF)
    {
        printf("ADC: reference voltage stored in eFuse Vref = %dmV\n", adc_config->adc_chars.vref);
    }
    else
    {
        printf("ADC: Default Vref = %dmV\n", adc_config->default_vref);
    }
#endif
}

void adc_single_init(adc_config_t *adc_config)
{
#if ESP_IDF_VERSION >= ESP_IDF_VERSION_VAL(5, 0, 0)
    // ESP-IDF 5.0+ 新版单次采样初始化
    adc_config->handle_shared = false;

    // 检查是否已有对应的ADC单元句柄
    if (adc_config->adc_unit == ADC_UNIT_1) {
        if (g_adc1_handle == NULL) {
            // 创建新的ADC1句柄
            adc_oneshot_unit_init_cfg_t init_config = {
                .unit_id = adc_config->adc_unit,
            };
            esp_err_t ret = adc_oneshot_new_unit(&init_config, &g_adc1_handle);
            if (ret != ESP_OK) {
                printf("ADC: Failed to create ADC1 unit (ret=%d)\n", ret);
                return;
            }
            printf("ADC: Created new ADC1 unit handle\n");
        }
        adc_config->adc_handle = g_adc1_handle;
        adc_config->handle_shared = true;
        g_adc1_ref_count++;
        printf("ADC: Using shared ADC1 handle (ref_count=%d)\n", g_adc1_ref_count);
    } else if (adc_config->adc_unit == ADC_UNIT_2) {
        if (g_adc2_handle == NULL) {
            // 创建新的ADC2句柄
            adc_oneshot_unit_init_cfg_t init_config = {
                .unit_id = adc_config->adc_unit,
            };
            esp_err_t ret = adc_oneshot_new_unit(&init_config, &g_adc2_handle);
            if (ret != ESP_OK) {
                printf("ADC: Failed to create ADC2 unit (ret=%d)\n", ret);
                return;
            }
            printf("ADC: Created new ADC2 unit handle\n");
        }
        adc_config->adc_handle = g_adc2_handle;
        adc_config->handle_shared = true;
        g_adc2_ref_count++;
        printf("ADC: Using shared ADC2 handle (ref_count=%d)\n", g_adc2_ref_count);
    }

    // 配置ADC通道
    adc_oneshot_chan_cfg_t config = {
        .bitwidth = adc_config->adc_width,
        .atten = adc_config->adc_atten,
    };
    ESP_ERROR_CHECK(adc_oneshot_config_channel(adc_config->adc_handle, adc_config->adc_channel, &config));
    printf("ADC: Configured channel %d with attenuation %d\n", adc_config->adc_channel, adc_config->adc_atten);
#else
    // ESP-IDF 4.x 旧版单次采样初始化
    // 配置ADC单位和位宽
    adc1_config_width(adc_config->adc_width);
    // 配置ADC通道和衰减
    adc1_config_channel_atten(adc_config->adc_channel, adc_config->adc_atten);
#endif
}

void adc_continuous_init(adc_config_t *adc_config)
{
#if ESP_IDF_VERSION >= ESP_IDF_VERSION_VAL(5, 0, 0)
    // ESP-IDF 5.0+ 新版连续采样初始化
    adc_continuous_handle_cfg_t adc_config_new = {
        .max_store_buf_size = 1024,
        .conv_frame_size = TIMES,
    };
    ESP_ERROR_CHECK(adc_continuous_new_handle(&adc_config_new, &adc_config->adc_continuous_handle));

    adc_continuous_config_t dig_cfg = {
        .sample_freq_hz = 10 * 1000,
        .conv_mode = ADC_CONV_MODE,
        .format = ADC_OUTPUT_TYPE,
    };

    adc_digi_pattern_config_t adc_pattern[SOC_ADC_PATT_LEN_MAX] = {0};
    dig_cfg.pattern_num = 1;

    uint8_t unit = GET_UNIT(adc_config->channel[0]);
    uint8_t ch = adc_config->channel[0] & 0x7;
    adc_pattern[0].atten = adc_config->adc_atten;
    adc_pattern[0].channel = ch;
    adc_pattern[0].unit = unit;
    adc_pattern[0].bit_width = SOC_ADC_DIGI_MAX_BITWIDTH;

    printf("ADC: adc_pattern[%d].atten is :%x\n", 0, adc_pattern[0].atten);
    printf("ADC: adc_pattern[%d].channel is :%x\n", 0, adc_pattern[0].channel);
    printf("ADC: adc_pattern[%d].unit is :%x\n", 0, adc_pattern[0].unit);

    dig_cfg.adc_pattern = adc_pattern;
    ESP_ERROR_CHECK(adc_continuous_config(adc_config->adc_continuous_handle, &dig_cfg));
#else
    // ESP-IDF 4.x 旧版连续采样初始化
    adc_digi_init_config_t adc_dma_config = {
        .max_store_buf_size = 1024,
        .conv_num_each_intr = TIMES,
        .adc1_chan_mask = adc_config->adc1_chan_mask,
        .adc2_chan_mask = adc_config->adc2_chan_mask,
    };
    ESP_ERROR_CHECK(adc_digi_initialize(&adc_dma_config));

    adc_digi_configuration_t dig_cfg = {
        .conv_limit_en = ADC_CONV_LIMIT_EN,
        .conv_limit_num = 250,
        .sample_freq_hz = 10 * 1000,
        .conv_mode = ADC_CONV_MODE,
        .format = ADC_OUTPUT_TYPE,
    };

    adc_digi_pattern_config_t adc_pattern[SOC_ADC_PATT_LEN_MAX] = {0};
    dig_cfg.pattern_num = 1;

    uint8_t unit = GET_UNIT(adc_config->channel[0]);
    uint8_t ch = adc_config->channel[0] & 0x7;
    adc_pattern[0].atten = adc_config->adc_atten;
    adc_pattern[0].channel = ch;
    adc_pattern[0].unit = unit;
    adc_pattern[0].bit_width = SOC_ADC_DIGI_MAX_BITWIDTH;

    printf("ADC: adc_pattern[%d].atten is :%x\n", 0, adc_pattern[0].atten);
    printf("ADC: adc_pattern[%d].channel is :%x\n", 0, adc_pattern[0].channel);
    printf("ADC: adc_pattern[%d].unit is :%x\n", 0, adc_pattern[0].unit);

    dig_cfg.adc_pattern = adc_pattern;
    ESP_ERROR_CHECK(adc_digi_controller_configure(&dig_cfg));
#endif
}

void adc_deinit(adc_config_t *adc_config)
{
#if ESP_IDF_VERSION >= ESP_IDF_VERSION_VAL(5, 0, 0)
    // ESP-IDF 5.0+ 清理资源
    if (adc_config->adc_handle && adc_config->handle_shared) {
        // 处理共享句柄的引用计数
        if (adc_config->adc_unit == ADC_UNIT_1) {
            g_adc1_ref_count--;
            printf("ADC: Released ADC1 handle reference (ref_count=%d)\n", g_adc1_ref_count);
            if (g_adc1_ref_count <= 0) {
                adc_oneshot_del_unit(g_adc1_handle);
                g_adc1_handle = NULL;
                g_adc1_ref_count = 0;
                printf("ADC: Deleted ADC1 unit handle\n");
            }
        } else if (adc_config->adc_unit == ADC_UNIT_2) {
            g_adc2_ref_count--;
            printf("ADC: Released ADC2 handle reference (ref_count=%d)\n", g_adc2_ref_count);
            if (g_adc2_ref_count <= 0) {
                adc_oneshot_del_unit(g_adc2_handle);
                g_adc2_handle = NULL;
                g_adc2_ref_count = 0;
                printf("ADC: Deleted ADC2 unit handle\n");
            }
        }
        adc_config->adc_handle = NULL;
        adc_config->handle_shared = false;
    } else if (adc_config->adc_handle && !adc_config->handle_shared) {
        // 处理非共享句柄
        adc_oneshot_del_unit(adc_config->adc_handle);
        adc_config->adc_handle = NULL;
        printf("ADC: Deleted non-shared ADC handle\n");
    }

    if (adc_config->adc_continuous_handle) {
        adc_continuous_deinit(adc_config->adc_continuous_handle);
        adc_config->adc_continuous_handle = NULL;
        printf("ADC: Deleted continuous ADC handle\n");
    }

    if (adc_config->cali_enabled && adc_config->cali_handle) {
        // 删除曲线拟合校准方案
        esp_err_t ret = adc_cali_delete_scheme_curve_fitting(adc_config->cali_handle);
        if (ret != ESP_OK) {
            printf("ADC: Warning - failed to delete calibration scheme\n");
        }
        adc_config->cali_handle = NULL;
        adc_config->cali_enabled = false;
        printf("ADC: Deleted calibration scheme\n");
    }
#else
    // ESP-IDF 4.x 清理资源
    adc_digi_deinitialize();
#endif
}

static bool check_valid_data(const adc_digi_output_data_t *data)
{
    const unsigned int unit = data->type2.unit;
    if (unit > 2)
        return false;
    if (data->type2.channel >= SOC_ADC_CHANNEL_NUM(unit))
        return false;

    return true;
}

float ReadVoltageContinuous(adc_config_t *adc_config)
{
    esp_err_t ret;
    uint8_t result[TIMES] = {0};
    uint32_t ret_num = 0;
    uint32_t voltage = 0, vol_num = 0, vol_total = 0;

    memset(result, 0xcc, TIMES);

#if ESP_IDF_VERSION >= ESP_IDF_VERSION_VAL(5, 0, 0)
    // ESP-IDF 5.0+ 新版连续采样读取
    ESP_ERROR_CHECK(adc_continuous_start(adc_config->adc_continuous_handle));
    ret = adc_continuous_read(adc_config->adc_continuous_handle, result, TIMES, &ret_num, ADC_MAX_DELAY);
#else
    // ESP-IDF 4.x 旧版连续采样读取
    adc_digi_start();
    ret = adc_digi_read_bytes(result, TIMES, &ret_num, ADC_MAX_DELAY);
#endif

    if (ret == ESP_OK)
    {
        for (int i = 0; i < ret_num; i += ADC_RESULT_BYTE)
        {
            adc_digi_output_data_t *p = (adc_digi_output_data_t *)&result[i];
            // adc_digi_output_data_t *p = reinterpret_cast<adc_digi_output_data_t *>(&result[i]);
            if (ADC_CONV_MODE == ADC_CONV_BOTH_UNIT || ADC_CONV_MODE == ADC_CONV_ALTER_UNIT)
            {
                if (check_valid_data(p))
                {
                    vol_num++;
                    vol_total += p->type2.data;
                    vTaskDelay(pdMS_TO_TICKS(1));
                }
                else
                {
                    printf("ADC: Invalid data [%d_%d_%x]\n", p->type2.unit + 1, p->type2.channel, p->type2.data);
                }
            }
        }

#if ESP_IDF_VERSION >= ESP_IDF_VERSION_VAL(5, 0, 0)
        // 新版校准API
        if (adc_config->cali_enabled) {
            adc_cali_raw_to_voltage(adc_config->cali_handle, vol_total / vol_num, (int *)&voltage);
        } else {
            // 如果校准不可用，使用默认计算
            voltage = (vol_total / vol_num) * adc_config->default_vref / 4095;
        }
#else
        // 旧版校准API
        voltage = esp_adc_cal_raw_to_voltage(vol_total / vol_num, &adc_config->adc_chars);
#endif
        vTaskDelay(pdMS_TO_TICKS(1));
    }
    else if (ret == ESP_ERR_TIMEOUT)
    {
        /**
         * ``ESP_ERR_TIMEOUT``: If ADC conversion is not finished until Timeout, you'll get this return error.
         * Here we set Timeout ``portMAX_DELAY``, so you'll never reach this branch.
         */
        printf("ADC: No data, increase timeout or reduce conv_num_each_intr");
        vTaskDelay(pdMS_TO_TICKS(1000));
    }

#if ESP_IDF_VERSION >= ESP_IDF_VERSION_VAL(5, 0, 0)
    ESP_ERROR_CHECK(adc_continuous_stop(adc_config->adc_continuous_handle));
#else
    adc_digi_stop();
#endif

    return (voltage * adc_config->voltage_divider_factor / 1000.0);
}

float ReadVoltageSingle(adc_config_t *adc_config)
{
    float calibration = 1.000;
    uint32_t voltage, adc_reading = 0;
    int raw_value;

#if ESP_IDF_VERSION >= ESP_IDF_VERSION_VAL(5, 0, 0)
    // ESP-IDF 5.0+ 新版单次采样读取
    if (adc_config->cali_enabled)
    {
        for (int i = 0; i < adc_config->adc_single_sample_num; i++)
        {
            ESP_ERROR_CHECK(adc_oneshot_read(adc_config->adc_handle, adc_config->adc_channel, &raw_value));
            adc_reading += raw_value;
            if (adc_config->adc_single_sample_num > 1)
                vTaskDelay(pdMS_TO_TICKS(adc_config->adc_single_sample_delay));
        }
        adc_reading /= adc_config->adc_single_sample_num;
        adc_cali_raw_to_voltage(adc_config->cali_handle, adc_reading, (int *)&voltage);
        return (voltage * adc_config->voltage_divider_factor / 1000.0);
    }
    else
    {
        // 如果校准不可用，使用Arduino的analogRead
        return (analogRead(adc_config->adc_pin) / 4095.0) * 3.3 * adc_config->voltage_divider_factor * (1100 / adc_config->default_vref) * calibration;
    }
#else
    // ESP-IDF 4.x 旧版单次采样读取
    if (adc_config->cal_type != ESP_ADC_CAL_VAL_DEFAULT_VREF)
    {
        for (int i = 0; i < adc_config->adc_single_sample_num; i++)
        {
            adc_reading += adc1_get_raw(adc_config->adc_channel);
            if (adc_config->adc_single_sample_num > 1)
                vTaskDelay(pdMS_TO_TICKS(adc_config->adc_single_sample_delay));
        }
        adc_reading /= adc_config->adc_single_sample_num;
        voltage = esp_adc_cal_raw_to_voltage(adc_reading, &adc_config->adc_chars);
        return (voltage * adc_config->voltage_divider_factor / 1000.0);
    }
    else
    {
        return (analogRead(adc_config->adc_pin) / 4095.0) * 3.3 * adc_config->voltage_divider_factor * (1100 / adc_config->default_vref) * calibration;
    }
#endif
}
