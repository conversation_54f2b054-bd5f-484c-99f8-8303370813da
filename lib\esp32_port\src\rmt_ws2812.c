#include "rmt_ws2812.h"
#include "driver/rmt.h"
#include "tuya_log.h"
#include "tuya_config.h"

#define RMT_TX_CHANNEL RMT_CHANNEL_0

// Timings for WS2812B from datasheet
// T0H: 0.4us (high pulse for 0)
// T0L: 0.85us (low pulse for 0)
// T1H: 0.8us (high pulse for 1)
// T1L: 0.45us (low pulse for 1)
// Using 80MHz APB clock, with a divider of 2 -> 40MHz clock, 1 tick = 25ns
#define T0H_TICKS (0.4 * 1000 / 25)  // 16 ticks
#define T0L_TICKS (0.85 * 1000 / 25) // 34 ticks
#define T1H_TICKS (0.8 * 1000 / 25)  // 32 ticks
#define T1L_TICKS (0.45 * 1000 / 25) // 18 ticks

static const char *TAG = "RMT_WS2812";
static rmt_item32_t bit0 = {{{T0H_TICKS, 1, T0L_TICKS, 0}}}; // WS2812 0-bit
static rmt_item32_t bit1 = {{{T1H_TICKS, 1, T1L_TICKS, 0}}}; // WS2812 1-bit

void rmt_ws2812_init(int gpio_num)
{
    rmt_config_t config = {
        .rmt_mode = RMT_MODE_TX,
        .channel = RMT_TX_CHANNEL,
        .gpio_num = gpio_num,
        .mem_block_num = 1,
        .tx_config = {
            .carrier_en = false,
            .idle_output_en = true,
            .idle_level = RMT_IDLE_LEVEL_LOW,
        },
        // The RMT clock source is the APB clock, which is fixed at 80MHz.
        // This is not affected by DFS (Dynamic Frequency Scaling) of the CPU.
        .clk_div = 2 // 80MHz / 2 = 40MHz, 1 tick = 25ns
    };

    ESP_ERROR_CHECK(rmt_config(&config));
    ESP_ERROR_CHECK(rmt_driver_install(config.channel, 0, 0));
    TY_LOGI("RMT driver installed for WS2812B on GPIO %d", gpio_num);
}

void rmt_ws2812_deinit(void)
{
    // Uninstall RMT driver
    esp_err_t err = rmt_driver_uninstall(RMT_TX_CHANNEL);
    if (err == ESP_OK) {
        TY_LOGI("RMT driver uninstalled for channel %d", RMT_TX_CHANNEL);
    } else if (err == ESP_ERR_INVALID_STATE) {
        // Driver not installed, which is not an error in this context.
        TY_LOGW("RMT driver was not installed, no action taken.");
    } else {
        // Log other potential errors
        TY_LOGE("Failed to uninstall RMT driver for channel %d: %s", RMT_TX_CHANNEL, esp_err_to_name(err));
    }
}

static void fill_item_from_byte(rmt_item32_t *item, uint8_t byte)
{
    for (int i = 7; i >= 0; i--) {
        if ((byte >> i) & 0x01) {
            *item = bit1;
        } else {
            *item = bit0;
        }
        item++;
    }
}

void rmt_ws2812_set_pixel(uint8_t r, uint8_t g, uint8_t b)
{
    // WS2812B expects data in GRB order
    uint8_t colors[3] = {g, r, b};
    rmt_item32_t items[24]; // 3 colors * 8 bits
    int item_idx = 0;

    for (int i = 0; i < 3; i++) {
        fill_item_from_byte(&items[item_idx], colors[i]);
        item_idx += 8;
    }

    // Wait for the previous transmission to finish
    rmt_wait_tx_done(RMT_TX_CHANNEL, portMAX_DELAY);
    // Write data
    ESP_ERROR_CHECK(rmt_write_items(RMT_TX_CHANNEL, items, 24, true));
}

// Helper function to scale brightness
uint8_t rmt_ws2812_scale8(uint8_t val, uint8_t scale) {
    return ((uint16_t)val * (uint16_t)scale) >> 8;
}

/**
 * @brief Set LED color and handle GPIO hold to prevent light leakage during sleep.
 *
 * @param r Red channel (0-255)
 * @param g Green channel (0-255)
 * @param b Blue channel (0-255)
 */
void rmt_ws2812_color_with_hold(uint8_t r, uint8_t g, uint8_t b) {
    static bool is_held = false;

    if (r == 0 && g == 0 && b == 0) {
        // If the LED is already off and held, do nothing.
        if (is_held) {
            return;
        }
        rmt_ws2812_set_pixel(0, 0, 0);
        // After turning off, enable hold on the pin to keep it low during sleep.
        gpio_hold_en((gpio_num_t)PIN_LED);
        is_held = true;
    } else {
        // If the pin was held, disable hold before sending new data.
        if (is_held) {
            gpio_hold_dis((gpio_num_t)PIN_LED);
            is_held = false;
        }
        rmt_ws2812_set_pixel(r, g, b);
    }
}
